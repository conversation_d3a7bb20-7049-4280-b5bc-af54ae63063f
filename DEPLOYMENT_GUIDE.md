# Deployment Guide for Vercel

This guide outlines all the fixes made to prepare the application for deployment on Vercel.

## Issues Fixed

### 1. Node.js Version Compatibility ✅
- **Issue**: Node.js 16.16.0 was too old for Next.js 15.2.4
- **Fix**: 
  - Added `.nvmrc` file specifying Node.js 20.11.0
  - Added `engines` field to `package.json` requiring Node.js >=20.0.0
  - Vercel will automatically use the correct Node.js version

### 2. TypeScript Errors ✅
- **Issue**: Property `session_id` did not exist on `CreateVoiceSessionResponse` type
- **Fix**: 
  - Updated `CreateVoiceSessionResponse` interface to include all required properties
  - Fixed debug page to use correct property name (`id` instead of `session_id`)

### 3. Dependencies and Imports ✅
- **Issue**: Duplicate files in `components/ui/` and `hooks/` directories
- **Fix**: 
  - Removed duplicate `use-mobile.tsx` and `use-toast.ts` from `components/ui/`
  - Kept proper versions in `hooks/` directory
  - All imports already correctly pointed to `@/hooks/`

### 4. Environment Variables ✅
- **Issue**: Inconsistent environment variable usage and hardcoded URLs
- **Fix**: 
  - Fixed LiveKit config to use `NEXT_PUBLIC_LIVEKIT_URL` instead of `LIVEKIT_URL`
  - Updated all API classes to use `NEXT_PUBLIC_BACKEND_URL` environment variable
  - Created comprehensive `.env.example` file with all required variables

### 5. Build Configuration ✅
- **Issue**: Potential conflicts with custom Vercel configuration
- **Fix**: 
  - Removed custom `vercel.json` file (Next.js has built-in Vercel support)
  - Ensured Next.js configuration is optimized for production builds

## Required Environment Variables

Set these in your Vercel dashboard:

```bash
# LiveKit Configuration (Required for voice chat)
NEXT_PUBLIC_LIVEKIT_URL=wss://your-livekit-server.livekit.cloud
LIVEKIT_API_KEY=your_livekit_api_key
LIVEKIT_API_SECRET=your_livekit_api_secret

# Backend API Configuration (Required)
NEXT_PUBLIC_BACKEND_URL=https://medbot-backend.fly.dev
NEXT_PUBLIC_API_URL=https://medbot-backend.fly.dev/api/v1
FLY_SERVER_URL=https://medbot-backend.fly.dev
```

## Deployment Steps

1. **Push to GitHub**: Ensure all changes are committed and pushed
2. **Connect to Vercel**: Import your repository in Vercel dashboard
3. **Set Environment Variables**: Add all required environment variables in Vercel project settings
4. **Deploy**: Vercel will automatically build and deploy your application

## Verification

After deployment, test these features:
- [ ] Application loads without errors
- [ ] Authentication works (login/register)
- [ ] Text chat functionality
- [ ] Voice chat interface (requires LiveKit configuration)
- [ ] Environment variables are properly loaded

## Troubleshooting

If you encounter issues:

1. **Build Errors**: Check Vercel build logs for specific error messages
2. **Environment Variables**: Verify all required variables are set in Vercel dashboard
3. **API Connectivity**: Ensure backend services are accessible from Vercel
4. **LiveKit Issues**: Verify LiveKit credentials and server URL

## Performance Optimizations

The application includes several optimizations for Vercel:
- Next.js 15.2.4 with App Router
- Automatic code splitting
- Image optimization
- CSS optimization
- Package import optimization for Radix UI and Lucide React

## Support

If you need help with deployment, check:
- Vercel documentation: https://vercel.com/docs
- Next.js deployment guide: https://nextjs.org/docs/deployment
- LiveKit documentation: https://docs.livekit.io/
